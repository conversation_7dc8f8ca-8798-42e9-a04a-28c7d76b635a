import React, {useEffect, useMemo, useRef, useState} from 'react';
import {
  Alert,
  Animated,
  Dimensions,
  Easing,
  Image,
  KeyboardAvoidingView,
  Pressable,
  SafeAreaView,
  ScrollView,
  StatusBar,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import BackgroundJapan from '../../assets/backgroundJapan';
import {useNavigation} from '@react-navigation/native';
import {useDispatch} from 'react-redux';
import {
  showSnackbar,
  ComponentStatus,
  FLoading,
  Winicon,
  AppButton,
  ListTile,
  FDialog,
  showDialog,
} from 'wini-mobile-components';
import {ColorThemes} from '../../assets/skin/colors';
import {TypoSkin} from '../../assets/skin/typography';
import {DataController} from '../../base/baseController';
import LocalAuthen from '../../features/local-authen/local-authen';
import {navigateReset, RootScreen} from '../../router/router';  
import {
  saveDataToAsyncStorage,
  getDataToAsyncStorage,
  removeDataToAsyncStorage,
} from '../../utils/AsyncStorage';
import {randomGID, regexPassWord} from '../../utils/Utils';
import {validatePhoneNumber} from '../../utils/validate';
import {TextFieldForm} from '../Default/form/component-form';
import {useForm} from 'react-hook-form';
import GoogleLogin, {
  webClientId,
} from '../../features/socials-login/GoogleSignIn/GoogleSignIn';
import {CustomerActions} from '../../redux/reducers/CustomerReducer';
import WScreenFooter from '../../Screen/Layout/footer';
import {StorageContanst} from '../../Config/Contanst';

export default function LoginScreen() {
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.8)).current;
  const methods = useForm({shouldFocusError: false});
  const [isSignUp, setSignUp] = useState(false);

  useEffect(() => {
    // Animate logo appearance
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 800,
        easing: Easing.out(Easing.back(1.5)),
        useNativeDriver: true,
      }),
    ]).start();
  });

  return (
    <SafeAreaView style={styles.container}>
      <Animated.View
        style={[
          styles.logoContainer,
          {
            flex: 1,
            opacity: fadeAnim,
            transform: [{scale: scaleAnim}],
          },
        ]}>
        <ScrollView
          showsVerticalScrollIndicator={false}
          style={{
            flex: 1,
            paddingTop: 24,
          }}>
          <View
            style={{
              flex: 1,
              width: '100%',
              justifyContent: 'center',
              alignContent: 'center',
              alignItems: 'center',
            }}>
            <Image
              source={require('../../assets/playstore.png')}
              height={150}
              style={{
                height: 150,
                aspectRatio: 1,
              }}
            />
            {isSignUp ? (
              <SignUpView methods={methods} isSignUp={setSignUp} />
            ) : (
              <View
                style={{
                  paddingHorizontal: 24,
                  flex: 1,
                  width: '100%',
                }}>
                <LoginView methods={methods} />
              </View>
            )}
          </View>
        </ScrollView>
        {/* signup */}
        <WScreenFooter
          style={{
            width: '100%',
            paddingHorizontal: 0,
          }}
          children={
            <ListTile
              title={
                isSignUp
                  ? 'Chuyển sang đăng nhập ngay?'
                  : 'Bạn chưa có tài khoản?'
              }
              style={{
                padding: 0,
                paddingHorizontal: 16,
                borderRadius: 0,
              }}
              titleStyle={[
                TypoSkin.body3,
                {color: ColorThemes.light.Neutral_Text_Color_Body},
              ]}
              trailing={
                <AppButton
                  title={isSignUp ? 'Đăng nhập' : 'Đăng ký'}
                  textColor={ColorThemes.light.Neutral_Text_Color_Subtitle}
                  textStyle={{...TypoSkin.buttonText3}}
                  containerStyle={{
                    height: 32,
                    borderRadius: 8,
                    paddingHorizontal: 12,
                  }}
                  borderColor={ColorThemes.light.transparent}
                  backgroundColor={
                    ColorThemes.light.Neutral_Background_Color_Main
                  }
                  onPress={() => {
                    setSignUp(!isSignUp);
                    methods.reset();
                  }}
                />
              }
            />
          }
        />
      </Animated.View>
    </SafeAreaView>
  );
}

// #region Login
const LoginView = ({methods}: {methods: any}) => {
  const [bio, setBio] = useState<any>('');
  const [isVisiblePass, setVisiblePass] = useState(true);
  const [isForgotPass, setForgotPass] = useState(false);
  const [loading, setLoading] = useState(false);
  const [loadingG, setLoadingG] = useState(false);

  const navigation = useNavigation<any>();
  const dispatch = useDispatch<any>();

  const validationForm = useMemo(() => {
    return (
      !methods.formState.errors.Mobile?.message &&
      methods.watch('Mobile')?.length > 0 &&
      methods.watch('Password')?.length > 0 &&
      !methods.formState.errors.Password?.message
    );
  }, [
    methods.watch('Mobile'),
    methods.formState.errors.Mobile?.message,
    methods.watch('Password'),
    methods.formState.errors.Password?.message,
  ]);

  useEffect(() => {
    getDataToAsyncStorage('Mobile').then(result => {
      methods.setValue('LastMobile', result);
      methods.setValue('Mobile', result);
    });
    getDataToAsyncStorage('spBiometrics').then(rs => {
      if (rs == 'true') {
        getDataToAsyncStorage('Biometrics').then(result => {
          if (result) {
            setBio(result);
          }
        });
      }
    });
  }, []);

  const customerController = new DataController('Customer');

  useEffect(() => {
    let timeoutVariable: string | number | NodeJS.Timeout | undefined;

    if (loading) {
      timeoutVariable = setTimeout(() => setLoading(false), 20000);
    }

    return () => clearTimeout(timeoutVariable);
  }, [loading]);

  const _loginAction = async () => {
    var mobile = methods.watch('Mobile')?.trim();
    var password = methods.watch('Password')?.trim();
    // Check if the number doesn't already start with 0 or +84
    if (/^(\+84|0)/.test(mobile)) {
      const val = validatePhoneNumber(mobile);
      if (!val) {
        methods.setError('Mobile', {message: 'Số điện thoại không hợp lệ'});
        return;
      }
    }
    setLoading(true);
    const res = await CustomerActions.login({
      type: 'account',
      email: mobile.includes('@') ? mobile : undefined,
      password: password,
      phone: !mobile.includes('@') ? mobile : undefined,
    });

    if (res.code === 200) {
      saveDataToAsyncStorage('timeRefresh', `${Date.now() / 1000 + 9 * 60}`);
      saveDataToAsyncStorage('accessToken', `${res.data.accessToken}`);
      saveDataToAsyncStorage('refreshToken', `${res.data.refreshToken}`);
      saveDataToAsyncStorage('Mobile', `${mobile}`);
      dispatch(CustomerActions.getInfor());
      showSnackbar({
        message: 'Đăng nhập thành công, Chào mừng bạn đã quay trở lại',
        status: ComponentStatus.SUCCSESS,
      });
      setLoading(false);
      navigateReset(RootScreen.navigateESchoolView);
    } else {
      showSnackbar({message: res.message, status: ComponentStatus.ERROR});
      setLoading(false);
    }
    setLoading(false);
  };

  const _loginSocial = async (value: any) => {
    if (value) {
      console.log('Google login success', value);
      setLoading(true);
      const res = await CustomerActions.login({
        type: 'google',
        ggClientId: webClientId,
        token: value.idToken,
      });
      if (res.code === 200) {
        await saveDataToAsyncStorage(
          StorageContanst.accessToken,
          res.data.accessToken,
        );
        dispatch(CustomerActions.getInfor());
        showSnackbar({
          message: 'Đăng nhập thành công',
          status: ComponentStatus.SUCCSESS,
        });
        navigateReset(RootScreen.navigateESchoolView);
        setLoading(false);
      } else {
        showSnackbar({message: res.message, status: ComponentStatus.ERROR});
        setLoading(false);
      }
    }
  };

  const _forgotPassword = async () => {
    var mobile = methods.watch('Mobile')?.trim();
    // Check if the number doesn't already start with 0 or +84
    if (!/^(\+84|0)/.test(mobile) && mobile) {
      mobile = '0' + mobile; // Add 0 at the beginning
    }
    navigation.push(RootScreen.ForgotPass, {isLogin: true, mobile: mobile});
  };

  return (
    <View
      pointerEvents={loading ? 'none' : 'auto'}
      style={{
        width: '100%',
        paddingBottom: 160,
      }}>
      <FLoading visible={loading} avt={require('../../assets/appstore.png')} />
      <KeyboardAvoidingView style={{width: '100%', gap: 24}}>
        <TextFieldForm
          control={methods.control}
          name="Mobile"
          placeholder="Nhập số điện thoại/Email"
          label="Số điện thoại/Email"
          returnKeyType="done"
          errors={methods.formState.errors}
          textFieldStyle={{
            height: 48,
            paddingHorizontal: 16,
            backgroundColor: ColorThemes.light.transparent,
          }}
          register={methods.register}
          onBlur={async (ev: string) => {
            if (ev === undefined || ev.length == 0) {
              methods.setError('Mobile', {
                message: 'Số điện thoại/ Email không được để trống',
              });
              return;
            }
            var mobile = ev.trim();
            // Check if the number doesn't already start with 0 or +84
            if (/^(\+84|0)/.test(mobile)) {
              const val = validatePhoneNumber(mobile);
              if (val) methods.clearErrors('Mobile');
              else
                methods.setError('Mobile', {
                  message: 'Số điện thoại không hợp lệ',
                });
            } else {
              // check for mail
            }
          }}
        />
        <TextFieldForm
          control={methods.control}
          name="Password"
          label="Mật khẩu"
          returnKeyType="done"
          placeholder={'Nhập mật khẩu của bạn'}
          errors={methods.formState.errors}
          secureTextEntry={isVisiblePass}
          textFieldStyle={{
            height: 48,
            backgroundColor: ColorThemes.light.transparent,
            paddingLeft: 16,
            paddingVertical: 16,
          }}
          register={methods.register}
          suffix={
            <TouchableOpacity
              style={{padding: 12}}
              onPress={() => {
                setVisiblePass(!isVisiblePass);
              }}>
              <Winicon
                src={
                  isVisiblePass
                    ? `outline/user interface/view`
                    : `outline/user interface/hide`
                }
                size={14}
              />
            </TouchableOpacity>
          }
          onBlur={async (ev: string) => {
            var pass = ev.trim();
            if (!pass)
              return methods.setError('Password', {
                message: 'Mật khẩu sai định dạng, hãy thử lại',
              });
            methods.clearErrors('Password');
          }}
        />
      </KeyboardAvoidingView>
      <View
        style={{
          paddingTop: methods.formState.errors.Password ? 12 : 0,
          flexDirection: 'row',
          width: '100%',
          justifyContent: 'space-between',
        }}>
        <TouchableOpacity
          onPress={_forgotPassword}
          style={{paddingVertical: 8, alignItems: 'flex-start'}}>
          <Text
            style={[
              TypoSkin.body3,
              {
                alignSelf: 'baseline',
                color: ColorThemes.light.Neutral_Text_Color_Subtitle,
              },
            ]}>
            Quên mật khẩu?
          </Text>
        </TouchableOpacity>
        <View />
      </View>
      {bio == 'true' ? (
        <View
          style={{
            paddingVertical: 8,
            justifyContent: 'center',
            alignContent: 'center',
            alignItems: 'center',
          }}>
          <LocalAuthen
            isFirstTime={
              methods.watch('LastMobile') !== methods.watch('Mobile') &&
              methods.watch('Mobile')?.length != 0
            }
            onSuccess={async value => {
              if (value === true) {
                setLoading(true);
                const mobile = await getDataToAsyncStorage('Mobile');
                const res = await CustomerActions.login(
                  mobile || methods.watch('Mobile'),
                );
                if (res.code === 200) {
                  saveDataToAsyncStorage(
                    'timeRefresh',
                    `${Date.now() / 1000 + 9 * 60}`,
                  );
                  saveDataToAsyncStorage(
                    'accessToken',
                    `${res.data.accessToken}`,
                  );
                  saveDataToAsyncStorage(
                    'refreshToken',
                    `${res.data.refreshToken}`,
                  );
                  dispatch(CustomerActions.getInfor());
                  showSnackbar({
                    message:
                      'Đăng nhập thành công, Chào mừng bạn đã quay trở lại',
                    status: ComponentStatus.SUCCSESS,
                  });
                  setLoading(false);
                  navigateReset(RootScreen.navigateESchoolView);
                }
              }
            }}
          />
        </View>
      ) : null}
      <View
        style={{
          flexDirection: 'row',
          width: '100%',
          gap: 8,
          alignItems: 'center',
        }}>
        <AppButton
          title={'Đăng nhập'}
          textColor={ColorThemes.light.Neutral_Background_Color_Absolute}
          textStyle={{
            ...TypoSkin.buttonText1,
            color: ColorThemes.light.Neutral_Background_Color_Absolute,
          }}
          disabled={!validationForm}
          containerStyle={{height: 48, flex: 1, borderRadius: 8, marginTop: 8}}
          borderColor={ColorThemes.light.Neutral_Border_Color_Main}
          backgroundColor={ColorThemes.light.Primary_Color_Main}
          onPress={() => {
            _loginAction();
          }}
        />
      </View>

      <AppButton
        title={'Bỏ qua đăng nhập'}
        textColor={ColorThemes.light.Primary_Color_Main}
        textStyle={{...TypoSkin.buttonText1}}
        containerStyle={{
          height: 48,
          alignSelf: 'center',
          borderRadius: 8,
          marginTop: 24,
        }}
        borderColor={ColorThemes.light.transparent}
        backgroundColor={ColorThemes.light.transparent}
        onPress={async () => {
          removeDataToAsyncStorage('timeRefresh');
          removeDataToAsyncStorage('accessToken');
          removeDataToAsyncStorage('refreshToken');
          removeDataToAsyncStorage('Mobile');

          navigation.reset({
            index: 0,
            routes: [{name: RootScreen.navigateESchoolView}],
          });
        }}
      />
      {/* social login */}
      <View
        style={{
          flexDirection: 'row',
          paddingBottom: 16,
          width: '100%',
          justifyContent: 'center',
        }}>
        <Text
          style={[
            TypoSkin.body3,
            {
              alignSelf: 'baseline',
              color: ColorThemes.light.Neutral_Text_Color_Subtitle,
            },
          ]}>
          Hoặc
        </Text>
      </View>
      <GoogleLogin
        onLoading={setLoadingG}
        isLoading={loadingG}
        onAuthSuccess={_loginSocial}
      />
    </View>
  );
};

// #region SignUp
const SignUpView = ({methods, isSignUp}: {methods: any; isSignUp: any}) => {
  const [isVisiblePass, setVisiblePass] = useState(true);
  const [isVisiblePass2, setVisiblePass2] = useState(true);
  const [loading, setLoading] = useState(false);
  const dialogRef = useRef<any>(null);
  const navigation = useNavigation<any>();
  const dispatch = useDispatch<any>();

  const validationForm = useMemo(() => {
    return (
      !methods.formState.errors.Mobile?.message &&
      methods.watch('Mobile')?.length > 0 &&
      methods.watch('Password')?.length > 0 &&
      !methods.formState.errors.Password?.message &&
      methods.watch('ConfirmPassword')?.length > 0 &&
      !methods.formState.errors.ConfirmPassword?.message
    );
  }, [
    methods.watch('Mobile'),
    methods.formState.errors.Mobile?.message,
    methods.watch('Password'),
    methods.formState.errors.Password?.message,
    methods.watch('ConfirmPassword'),
    methods.formState.errors.ConfirmPassword?.message,
  ]);

  useEffect(() => {
    methods.setValue('Mobile', undefined);
    setLoading(false);
  }, []);

  useEffect(() => {
    let timeoutVariable: string | number | NodeJS.Timeout | undefined;

    if (loading) {
      timeoutVariable = setTimeout(() => setLoading(false), 20000);
    }

    return () => clearTimeout(timeoutVariable);
  }, [loading]);

  const onCheckPhone = async () => {
    var mobile = methods.watch('Mobile').trim();
    var email = methods.watch('Name')?.trim();

    // Check if the number doesn't already start with 0 or +84
    if (/^(\+84|0)/.test(mobile)) {
      if (!validatePhoneNumber(mobile)) {
        methods.setError('Mobile', {message: 'Số điện thoại không hợp lệ'});
        return false;
      }
    }

    setLoading(true);

    // check sdt bi khoa
    // check sdt email da dang ky
    const resCustomers = await customerController.aggregateList({
      page: 1,
      size: 1,
      searchRaw: `(@Mobile:("${mobile}")) | (@Email:("${email}"))`,
    });
    if (resCustomers) {
      if (resCustomers?.data?.length > 0) {
        showSnackbar({
          message: 'Số điện thoại hoặc email đã đăng ký trước đó',
          status: ComponentStatus.ERROR,
        });
        setLoading(false);
        return;
      }
    } else {
      showSnackbar({
        message: 'Đã có lỗi xảy ra',
        status: ComponentStatus.ERROR,
      });
      setLoading(false);
      return;
    }

    setLoading(false);
    return true;
  };

  const customerController = new DataController('Customer');

  const _signUp = async () => {
    var email = methods.watch('Name')?.trim();
    var mobile = methods.watch('Mobile')?.trim();
    var password = methods.watch('Password')?.trim();
    // Check if the number doesn't already start with 0 or +84
    if (!/^(\+84|0)/.test(mobile)) {
      mobile = '0' + mobile; // Add 0 at the beginning
    }

    const val = validatePhoneNumber(mobile);
    if (!val) {
      methods.setError('Mobile', {message: 'Số điện thoại không hợp lệ'});
      return;
    }

    setLoading(true);

    const deviceToken = await getDataToAsyncStorage('fcmToken');
    const hashPass = await CustomerActions.hashPassword(password);

    if (hashPass.code != 200) {
      setLoading(false);
      showSnackbar({
        message: hashPass.message,
        status: ComponentStatus.ERROR,
      });
      return;
    }
    const newCus = {
      Id: randomGID(),
      Email: email,
      Name: email.split('@')[0],
      DateCreated: Date.now(),
      Mobile: mobile,
      Password: hashPass.data,
      DeviceToken: deviceToken,
    };
    const customerRes = await customerController.add([newCus]);
    if (customerRes.code == 200) {
      isSignUp(false);
      showSnackbar({
        message: 'Đăng ký thành công, hãy đăng nhập để sử dụng',
        status: ComponentStatus.SUCCSESS,
      });
      setLoading(false);
    } else {
      showSnackbar({
        message: customerRes.message ?? 'Đã có lỗi xảy ra khi tạo tài khoản.',
        status: ComponentStatus.ERROR,
      });
      setLoading(false);
      return;
    }
    setLoading(false);
  };

  return (
    <ScrollView
      showsVerticalScrollIndicator={false}
      style={{
        width: Dimensions.get('screen').width,
        flex: 1,
        paddingHorizontal: 24,
      }}>
      <FLoading visible={loading} avt={require('../../assets/appstore.png')} />
      <FDialog ref={dialogRef} />
      <KeyboardAvoidingView
        behavior="padding"
        shouldRasterizeIOS
        style={{width: '100%', flex: 1}}>
        <View style={{width: '100%', gap: 16, flex: 1}}>
          <TextFieldForm
            control={methods.control}
            name="Name"
            placeholder="Email"
            label="Email"
            returnKeyType="done"
            errors={methods.formState.errors}
            textFieldStyle={{
              height: 48,
              padding: 16,
              backgroundColor: ColorThemes.light.transparent,
            }}
            register={methods.register}
            type="email-address"
            onBlur={(ev: string) => {
              if (ev?.length !== 0) methods.clearErrors('Name');
              else
                methods.setError('Name', {
                  message: 'Email không được để trống',
                });
            }}
          />
          <TextFieldForm
            control={methods.control}
            name="Mobile"
            required
            label="Số điện thoại"
            placeholder="Nhập số điện thoại của bạn"
            returnKeyType="done"
            errors={methods.formState.errors}
            textFieldStyle={{
              height: 48,
              backgroundColor: ColorThemes.light.transparent,
            }}
            register={methods.register}
            prefix={
              <View
                style={{
                  flexDirection: 'row',
                  height: 46,
                  paddingHorizontal: 8,
                  alignItems: 'center',
                  justifyContent: 'center',
                  gap: 8,
                  backgroundColor:
                    ColorThemes.light.Neutral_Background_Color_Main,
                  borderRadius: 8,
                }}>
                <Text
                  style={{
                    ...TypoSkin.buttonText3,
                    color: ColorThemes.light.Neutral_Text_Color_Title,
                  }}>
                  +84
                </Text>
                <Winicon src="outline/arrows/down-arrow" size={16} />
              </View>
            }
            type="number-pad"
            onBlur={async (ev: string) => {
              if (ev === undefined || ev.length == 0) {
                methods.setError('Mobile', {
                  message: 'Số điện thoại không hợp lệ',
                });
                return;
              }
              var mobile = ev.trim();
              // Check if the number doesn't already start with 0 or +84
              if (!/^(\+84|0)/.test(mobile)) {
                mobile = '0' + mobile; // Add 0 at the beginning
              }
              const val = validatePhoneNumber(mobile);
              if (val) methods.clearErrors('Mobile');
              else
                methods.setError('Mobile', {
                  message: 'Số điện thoại không hợp lệ',
                });
            }}
          />
          <View style={{width: '100%', gap: 8, paddingBottom: 12}}>
            <TextFieldForm
              control={methods.control}
              name="Password"
              secureTextEntry={isVisiblePass}
              label="Mật khẩu"
              returnKeyType="done"
              placeholder={'Tạo mật khẩu của bạn'}
              suffix={
                <TouchableOpacity
                  style={{padding: 12}}
                  onPress={() => {
                    setVisiblePass(!isVisiblePass);
                  }}>
                  <Winicon
                    src={
                      isVisiblePass
                        ? `outline/user interface/view`
                        : `outline/user interface/hide`
                    }
                    size={14}
                  />
                </TouchableOpacity>
              }
              errors={methods.formState.errors}
              textFieldStyle={{
                height: 48,
                backgroundColor: ColorThemes.light.transparent,
                paddingLeft: 16,
                paddingVertical: 16,
                marginBottom: methods.formState.errors.Password?.message
                  ? 16
                  : 8,
              }}
              register={methods.register}
              onBlur={async (ev: string) => {
                if (ev === undefined || ev.length == 0) {
                  methods.setError('Password', {
                    message: 'Mật khẩu không được để trống',
                  });
                  return;
                }
                var pass = ev.trim();
                if (!regexPassWord.test(pass))
                  return methods.setError('Password', {
                    message: 'Mật khẩu sai định dạng, hãy thử lại',
                  });
                methods.clearErrors('Password');
              }}
            />
            <Text
              style={[
                TypoSkin.subtitle4,
                {
                  alignSelf: 'baseline',
                  color: ColorThemes.light.Neutral_Text_Color_Subtitle,
                },
              ]}>{`- Tối thiểu 8 ký tự/ Tối đa 16 ký tự \n- Gồm chữ hoa, thường và số`}</Text>
            <TextFieldForm
              control={methods.control}
              name="ConfirmPassword"
              label="Nhập lại mật khẩu"
              returnKeyType="done"
              secureTextEntry={isVisiblePass2}
              suffix={
                <TouchableOpacity
                  style={{padding: 12}}
                  onPress={() => {
                    setVisiblePass2(!isVisiblePass2);
                  }}>
                  <Winicon
                    src={
                      isVisiblePass2
                        ? `outline/user interface/view`
                        : `outline/user interface/hide`
                    }
                    size={14}
                  />
                </TouchableOpacity>
              }
              placeholder={'Nhập lại mật khẩu của bạn'}
              errors={methods.formState.errors}
              textFieldStyle={{
                height: 48,
                backgroundColor: ColorThemes.light.transparent,
                paddingLeft: 16,
                paddingVertical: 16,
              }}
              register={methods.register}
              onBlur={async (ev: string) => {
                if (ev === undefined || ev.length == 0) {
                  methods.setError('ConfirmPassword', {
                    message: 'Mật khẩu không được để trống',
                  });
                  return;
                }
                var rePass = ev.trim();
                if (methods.watch('Password') !== rePass)
                  return methods.setError('ConfirmPassword', {
                    message: 'Mật khẩu nhập lại không đúng',
                  });
                methods.clearErrors('ConfirmPassword');
              }}
            />
            <TextFieldForm
              control={methods.control}
              name="Address"
              placeholder="Địa chỉ"
              returnKeyType="done"
              errors={methods.formState.errors}
              textFieldStyle={{
                height: 48,
                padding: 16,
                backgroundColor: ColorThemes.light.transparent,
              }}
              register={methods.register}
            />
          </View>
        </View>
        <View
          style={{
            flexDirection: 'row',
            width: '100%',
            paddingTop: 16,
            paddingBottom: 130,
          }}>
          <AppButton
            title={'Đăng ký'}
            textColor={ColorThemes.light.Neutral_Background_Color_Absolute}
            textStyle={{...TypoSkin.buttonText1}}
            disabled={!validationForm || loading}
            containerStyle={{
              height: 48,
              flex: 1,
              borderRadius: 8,
              marginTop: 8,
            }}
            borderColor={ColorThemes.light.Neutral_Border_Color_Main}
            backgroundColor={ColorThemes.light.Primary_Color_Main}
            onPress={async () => {
              const check = await onCheckPhone();
              if (check) {
                _signUp();
              }
            }}
          />
        </View>
      </KeyboardAvoidingView>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
    alignItems: 'center',
    justifyContent: 'center',
  },
  logoContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
});
