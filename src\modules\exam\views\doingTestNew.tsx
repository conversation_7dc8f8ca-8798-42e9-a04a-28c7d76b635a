/* eslint-disable react-native/no-inline-styles */
import {
  ActivityIndicator,
  Alert,
  Dimensions,
  FlatList,
  Image,
  Platform,
  ScrollView,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import {ColorThemes} from '../../../assets/skin/colors';
import ScreenHeader from '../../../Screen/Layout/header';
import {navigateBack, RootScreen} from '../../../router/router';
import {
  AppButton,
  Checkbox,
  FBottomSheet,
  hideBottomSheet,
  showBottomSheet,
  Winicon,
} from 'wini-mobile-components';
import {TypoSkin} from '../../../assets/skin/typography';
import {useCallback, useEffect, useMemo, useRef, useState, memo} from 'react';
import {RadioButton} from 'react-native-paper';
import ClickableImage from '../components/ClickableImage';
import {useDispatch} from 'react-redux';
import store, {AppDispatch} from '../../../redux/store/store';
import {useNavigation, useRoute} from '@react-navigation/native';
import {ExamActions} from '../../../redux/reducers/examReducer';
import {SafeAreaView} from 'react-native-safe-area-context';
import {useTranslation} from 'react-i18next';
import RenderHTML from 'react-native-render-html';
import {DataController} from '../../../base/baseController';
import {StatusExam} from '../../../Config/Contanst';
import {randomGID} from '../../../utils/Utils';
import ConfigAPI from '../../../Config/ConfigAPI';
import Sound from 'react-native-sound';
import { LessonActions } from '../../../redux/reducers/proccessLessonReducer';

// Global helper function to safely render HTML content
const getSafeHtmlContent = (
  content: any,
  fallback: string = '<p>No content</p>',
) => {
  try {
    if (!content) {
      console.log('⚠️ getSafeHtmlContent: content is null/undefined');
      return fallback;
    }
    if (typeof content !== 'string') {
      console.log(
        '⚠️ getSafeHtmlContent: content is not string:',
        typeof content,
        content,
      );
      return fallback;
    }
    if (content.trim() === '') {
      console.log('⚠️ getSafeHtmlContent: content is empty string');
      return fallback;
    }
    console.log('✅ getSafeHtmlContent: valid content');
    return content;
  } catch (error) {
    console.error('❌ getSafeHtmlContent error:', error);
    return fallback;
  }
};

// Memoized Answer Component for better performance
const AnswerItem = memo(
  ({
    answer,
    questionId,
    selectionType,
    onSelect,
  }: {
    answer: any;
    questionId: string;
    selectionType: number;
    onSelect: (questionId: string, answerId: string) => void;
  }) => {
    const handlePress = useCallback(() => {
      onSelect(questionId, answer.Id);
    }, [onSelect, questionId, answer.Id]);

    return (
      <TouchableOpacity
        onPress={handlePress}
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          paddingHorizontal: 12,
          paddingVertical: 4,
          borderRadius: 8,
          borderWidth: 1,
          borderColor: answer.choose
            ? ColorThemes.light.Info_Color_Main
            : ColorThemes.light.Neutral_Border_Color_Main,
          backgroundColor: answer.choose
            ? ColorThemes.light.primary_background
            : ColorThemes.light.Neutral_Background_Color_Absoluteary_Color_Main,
        }}>
        {/* Answer Selection Icon */}
        <View style={{marginRight: 12}} pointerEvents="none">
          {selectionType === 2 ? (
            <Checkbox
              value={answer.choose ?? false}
              onChange={() => {}} // Disabled - handled by TouchableOpacity
              checkboxStyle={{
                backgroundColor: ColorThemes.light.Info_Color_Main,
              }}
            />
          ) : (
            <RadioButton.Android
              value={answer.Id}
              status={answer.choose ? 'checked' : 'unchecked'}
              color={ColorThemes.light.Info_Color_Main}
              onPress={() => {}} // Disabled - handled by TouchableOpacity
            />
          )}
        </View>

        {/* Answer Content */}
        <View style={{flex: 1}}>
          <Text
            style={{
              ...TypoSkin.body2,
              color: answer.choose
                ? ColorThemes.light.Info_Color_Main
                : ColorThemes.light.neutral_text_body_color,
            }}>
            {answer.Content || answer.Name}
          </Text>
        </View>
      </TouchableOpacity>
    );
  },
);

// Memoized Question Component
const QuestionItem = memo(
  ({
    question,
    questionIndex,
    onAnswerSelect,
    t,
  }: {
    question: any;
    questionIndex: number;
    onAnswerSelect: (questionId: string, answerId: string) => void;
    t: any;
  }) => {
    return (
      <View style={{marginBottom: 24, paddingHorizontal: 16}}>
        {/* Question Header */}
        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            marginBottom: 4,
            paddingVertical: 8,
          }}>
          <Text
            style={{
              ...TypoSkin.subtitle2,
              fontWeight: '700',
              color: ColorThemes.light.Neutral_Text_Color_Title,
              marginRight: 8,
            }}>
            {`${t('exam.question')} ${questionIndex + 1}:`}
          </Text>
          <Text
            style={{
              ...TypoSkin.body3,
              color: ColorThemes.light.neutral_text_subtitle_color,
            }}>
            ({' '}
            {question.SelectionType === 2
              ? t('exam.selectMultipleAnswers')
              : t('exam.selectOneAnswer')}{' '}
            )
          </Text>
        </View>

        {/* Question Content */}
        <View style={{marginBottom: 16}}>
          <RenderHTML
            contentWidth={Dimensions.get('window').width - 32}
            source={{
              html: getSafeHtmlContent(
                question.Content || question.Name,
                '<p>No content available</p>',
              ),
            }}
            tagsStyles={{
              body: {
                fontSize: 16,
                lineHeight: 24,
                fontFamily: 'Inter',
              },
              p: {
                fontSize: 16,
                lineHeight: 24,
                fontFamily: 'Inter',
              },
              img: {
                marginVertical: 10,
                alignSelf: 'center',
                borderRadius: 8,
                borderWidth: 1,
                maxWidth: '100%',
                height: 200,
              },
            }}
            renderers={{
              img: ({TDefaultRenderer, ...props}: any) => {
                const {src} = props.tnode.attributes;
                if (src) {
                  return (
                    <ClickableImage
                      source={{uri: src}}
                      style={{
                        marginVertical: 10,
                        alignSelf: 'center',
                        borderRadius: 8,
                        borderWidth: 1,
                        maxWidth: '100%',
                        height: 200,
                      }}
                      resizeMode="contain"
                    />
                  );
                }
                return <TDefaultRenderer {...props} />;
              },
            }}
          />
        </View>

        {/* Answers */}
        <View style={{gap: 8}}>
          {question.lstAnswer?.map((answer: any) => (
            <AnswerItem
              key={answer.Id}
              answer={answer}
              questionId={question.Id}
              selectionType={question.SelectionType}
              onSelect={onAnswerSelect}
            />
          ))}
        </View>
      </View>
    );
  },
);

export default function DoingTestNew() {
  const {t} = useTranslation();
  const dispatch: AppDispatch = useDispatch();
  const route = useRoute<any>();
  const {lstExam, listQuestions, lessonData} = route.params;
  // State management - simplified without sections
  const [exams, setExams] = useState<any[]>([]);
  const [allQuestions, setAllQuestions] = useState<any[]>([]);
  const [currentQuestions, setCurrentQuestions] = useState<any[]>([]);
  const [selectedExam, setSelectedExam] = useState<any>(null);
  const [currentExamIndex, setCurrentExamIndex] = useState(0);
  const [loading, setLoading] = useState(true);
  //navigate
  const navigation = useNavigation<any>();
  // Controllers
  const testResultController = useMemo(
    () => new DataController('Test_Result'),
    [],
  );
  const answerController = useMemo(() => new DataController('Answer'), []);

  // Use ref to store user answers to prevent race conditions
  const userAnswersRef = useRef<{
    [questionId: string]: {[answerId: string]: boolean};
  }>({});

  const bottomSheetRef = useRef<any>(null);

  // State for exam media collapse
  const [isPlayingAudio, setIsPlayingAudio] = useState<any>(false);

  // Helper function to apply user answers to questions
  const applyUserAnswersToQuestions = useCallback((questions: any[]) => {
    return questions.map(question => {
      if (!question.lstAnswer) {
        return question;
      }

      return {
        ...question,
        lstAnswer: question.lstAnswer.map((answer: any) => ({
          ...answer,
          choose: userAnswersRef.current[question.Id]?.[answer.Id] || false,
        })),
      };
    });
  }, []);

  // Helper function to get questions with preserved answers
  const getQuestionsWithAnswers = useCallback(
    (questionIds: string[]) => {
      const questions = questionIds
        .map((questionId: any) => {
          return allQuestions.find((q: any) => q.Id === questionId);
        })
        .filter(Boolean);

      return applyUserAnswersToQuestions(questions);
    },
    [allQuestions, applyUserAnswersToQuestions],
  );

  // Initialize data from props
  useEffect(() => {
    if (lstExam && listQuestions) {
      console.log('🔄 Initializing data from props...');

      // Set exams data
      const sortedExams = [...lstExam].sort((a: any, b: any) => (a.Sort || 0) - (b.Sort || 0));
      setExams(sortedExams);

      // Set questions data first (without answers)
      console.log(`📋 Set ${listQuestions.length} questions`);
      setAllQuestions(listQuestions);

      // Auto select first exam
      if (sortedExams.length > 0) {
        setSelectedExam(sortedExams[0]);
        setCurrentExamIndex(0);

        // Set initial questions for first exam
        const questionIds = sortedExams[0]?.QuestionId?.split(',') || [];
        const examQuestions = questionIds
          .map((questionId: any) => {
            return listQuestions.find((q: any) => q.Id === questionId);
          })
          .filter(Boolean);

        setCurrentQuestions(examQuestions);
        console.log(`📋 Set ${examQuestions.length} questions for first exam`);
      }

      setLoading(false);
    }
  }, [lstExam, listQuestions]);

  // Load answers from API and combine with questions
  useEffect(() => {
    if (allQuestions.length > 0) {
      console.log('🔄 Loading answers for questions...', allQuestions.length);

      answerController
        .getListSimple({
          query: `@QuestionId:{${allQuestions.map(e => e.Id).join(' | ')}}`,
          returns: ['Id', 'Name', 'Content', 'Sort', 'QuestionId', 'IsResult'],
          sortby: {BY: 'Sort', DIRECTION: 'ASC'},
        })
        .then(res => {
          if (res.code === 200) {
            console.log(
              '✅ Answers loaded, combining with questions...',
              res.data.length,
            );
            // Combine questions with answers and update allQuestions
            const questionsWithAnswers = allQuestions.map((question: any) => ({
              ...question,
              lstAnswer: res.data
                .filter((answer: any) => answer.QuestionId === question.Id)
                .map((answer: any) => ({
                  ...answer,
                  choose: false, // Initialize as not chosen
                })),
            }));

            console.log(
              '📋 Questions with answers:',
              questionsWithAnswers[0]?.lstAnswer?.length,
            );
            setAllQuestions(questionsWithAnswers);

            // Update currentQuestions if they exist
            if (currentQuestions.length > 0) {
              const updatedCurrentQuestions = currentQuestions.map(
                (question: any) => {
                  const questionWithAnswers = questionsWithAnswers.find(
                    (q: any) => q.Id === question.Id,
                  );
                  if (questionWithAnswers) {
                    // Apply user answers from ref
                    return {
                      ...questionWithAnswers,
                      lstAnswer: questionWithAnswers.lstAnswer.map(
                        (answer: any) => ({
                          ...answer,
                          choose:
                            userAnswersRef.current[question.Id]?.[answer.Id] ||
                            false,
                        }),
                      ),
                    };
                  }
                  return question;
                },
              );
              console.log('🔄 Updated currentQuestions with answers');
              setCurrentQuestions(updatedCurrentQuestions);
            }
          } else {
            console.log('❌ Failed to load answers:', res);
          }
        })
        .catch(error => {
          console.log('❌ Error loading answers:', error);
        });
    }
  }, [allQuestions.length, answerController]); // Remove currentQuestions.length to avoid infinite loop

  // Handle exam navigation - simplified without sections
  const handleNextExam = () => {
    if (currentExamIndex < exams.length - 1) {
      const nextExam = exams[currentExamIndex + 1];
      setSelectedExam(nextExam);
      setCurrentExamIndex(currentExamIndex + 1);
      const questionIds = nextExam?.QuestionId?.split(',');
      const examQuestions = getQuestionsWithAnswers(questionIds);
      setCurrentQuestions(examQuestions);
    }
  };

  const handlePreviousExam = () => {
    if (currentExamIndex > 0) {
      const prevExam = exams[currentExamIndex - 1];
      setSelectedExam(prevExam);
      setCurrentExamIndex(currentExamIndex - 1);
      const questionIds = prevExam?.QuestionId?.split(',');
      const examQuestions = getQuestionsWithAnswers(questionIds);
      setCurrentQuestions(examQuestions);
    }
  };

  // Handle answer selection - Preserve answers across sections/exams
  const handleAnswerSelect = useCallback(
    (questionId: string, answerId: string) => {
      console.log(`🎯 Selecting answer ${answerId} for question ${questionId}`);
      dispatch(ExamActions.choose(questionId, answerId));

      // Find the question to get SelectionType
      const question = currentQuestions.find(q => q.Id === questionId);
      if (!question) {
        console.log(`❌ Question ${questionId} not found`);
        return;
      }

      // Update userAnswersRef first
      if (!userAnswersRef.current[questionId]) {
        userAnswersRef.current[questionId] = {};
      }

      if (question.SelectionType === 1) {
        // Single choice - clear all answers for this question first
        userAnswersRef.current[questionId] = {};
        userAnswersRef.current[questionId][answerId] = true;
      } else {
        // Multiple choice - toggle this answer
        const currentValue =
          userAnswersRef.current[questionId][answerId] || false;
        userAnswersRef.current[questionId][answerId] = !currentValue;
      }

      console.log(
        '💾 Saved to userAnswersRef:',
        userAnswersRef.current[questionId],
      );

      // Update currentQuestions with new answers
      setCurrentQuestions(prevQuestions => {
        return prevQuestions.map(q => {
          if (q.Id === questionId) {
            return {
              ...q,
              lstAnswer: q.lstAnswer.map((answer: any) => ({
                ...answer,
                choose:
                  userAnswersRef.current[questionId]?.[answer.Id] || false,
              })),
            };
          }
          return q;
        });
      });

      console.log(`✅ Updated currentQuestions for question ${questionId}`);
    },
    [dispatch, currentQuestions],
  );

  // Function to collect all answers and calculate score - simplified without sections
  const collectAnswersForSubmit = useCallback(() => {
    const answeredQuestions: string[] = [];
    const selectedAnswers: string[] = [];
    let totalScore = 0;
    let correctAnswers = 0;

    // Loop through all questions to collect answers and calculate scores
    allQuestions.forEach((question: any) => {
      const userAnswers = userAnswersRef.current[question.Id];

      if (userAnswers && Object.keys(userAnswers).length > 0) {
        // Get selected answer IDs for this question
        const selectedAnswerIds = Object.keys(userAnswers).filter(
          answerId => userAnswers[answerId] === true,
        );

        if (selectedAnswerIds.length > 0) {
          answeredQuestions.push(question.Id);
          selectedAnswers.push(...selectedAnswerIds);

          // Calculate score for this question
          const correctAnswerIds =
            question.lstAnswer
              ?.filter(
                (answer: any) =>
                  answer.IsResult === true || answer.IsResult === 1,
              )
              ?.map((answer: any) => answer.Id) || [];

          // Check if user's answers match correct answers
          const isCorrect =
            question.SelectionType === 1
              ? selectedAnswerIds.length === 1 &&
                correctAnswerIds.includes(selectedAnswerIds[0])
              : selectedAnswerIds.length === correctAnswerIds.length &&
                selectedAnswerIds.every((id: string) =>
                  correctAnswerIds.includes(id),
                ) &&
                correctAnswerIds.every((id: string) =>
                  selectedAnswerIds.includes(id),
                );

          if (isCorrect) {
            const questionScore = question.Score || 1;
            totalScore += questionScore;
            correctAnswers++;
          }
        }
      }
    });

    // Check pass/fail logic - simplified
    let isPassed = true;
    let failReason = '';

    // Check overall pass score
    const passScore = lessonData?.QuizzScore || 0; // Điểm đạt của lesson
    if (totalScore < passScore && passScore > 0) {
      isPassed = false;
      failReason = `Điểm tổng không đạt: ${totalScore}/${passScore}`;
    }

    console.log(
      `🎯 Pass/Fail: ${isPassed ? 'PASS' : 'FAIL'} - ${
        failReason || 'Đạt yêu cầu'
      }`,
    );

    return {
      questionIds: answeredQuestions.join(','),
      answerIds: selectedAnswers.join(','),
      totalScore,
      correctAnswers,
      totalQuestions: allQuestions.length,
      answeredCount: answeredQuestions.length,
      isPassed,
      failReason,
      passScore: lessonData?.QuizzScore || 0,
    };
  }, [allQuestions, lessonData]);

  // Function to create detailed result data for results screen - simplified without sections
  const createDetailedResultData = useCallback(() => {
    const basicResult = collectAnswersForSubmit();

    // Create detailed exams with questions
    const detailedExams = exams.map((exam: any) => {
      const examQuestionIds = exam.QuestionId?.split(',') || [];

      // Get questions for this exam
      const examQuestions = examQuestionIds
        .map((questionId: string) => {
          const question = allQuestions.find(
            (q: any) => q.Id === questionId,
          );
          if (!question) return null;

          const userAnswers = userAnswersRef.current[questionId] || {};
          const selectedAnswerIds = Object.keys(userAnswers).filter(
            answerId => userAnswers[answerId] === true,
          );

          // Get correct answers
          const correctAnswerIds =
            question.lstAnswer
              ?.filter(
                (answer: any) =>
                  answer.IsResult === true || answer.IsResult === 1,
              )
              ?.map((answer: any) => answer.Id) || [];

          // Check if question is answered and correct
          const isAnswered = selectedAnswerIds.length > 0;
          const isCorrect =
            isAnswered &&
            (question.SelectionType === 1
              ? selectedAnswerIds.length === 1 &&
                correctAnswerIds.includes(selectedAnswerIds[0])
              : selectedAnswerIds.length === correctAnswerIds.length &&
                selectedAnswerIds.every((id: string) =>
                  correctAnswerIds.includes(id),
                ) &&
                correctAnswerIds.every((id: string) =>
                  selectedAnswerIds.includes(id),
                ));

          return {
            id: question.Id,
            isCorrect,
          };
        })
        .filter(Boolean);

      return {
        id: exam.Id,
        name: exam.Name || `Exam ${exam.Sort || 1}`,
        sort: exam.Sort || 1,
        questions: examQuestions,
      };
    }).sort((a: any, b: any) => a.sort - b.sort);

    // Calculate max total score
    const maxTotalScore = allQuestions.reduce(
      (sum: number, question: any) => sum + (question.Score || 1),
      0,
    );

    // Create final result object
    const detailedResult = {
      // Basic lesson info
      lessonId: lessonData?.Id || 'unknown',
      lessonName: lessonData?.Name || 'Lesson',
      completedAt: new Date().toISOString(),

      // Overall scores
      totalScore: basicResult.totalScore,
      maxTotalScore,
      passScore: basicResult.passScore,
      isPassed: basicResult.isPassed,
      failReason: basicResult.failReason,
      answeredCount: basicResult.answeredCount,
      // Question statistics
      totalQuestions: basicResult.totalQuestions,
      answeredQuestions: basicResult.answeredCount,
      correctAnswers: basicResult.correctAnswers,

      // For API submission
      questionIds: basicResult.questionIds,
      answerIds: basicResult.answerIds,

      // Detailed breakdown for results screen
      exams: detailedExams,
    };

    console.log('📊 Detailed Result Data:', detailedResult);
    return detailedResult;
  }, [
    collectAnswersForSubmit,
    exams,
    allQuestions,
    lessonData,
    userAnswersRef,
  ]);

  const ConFirmEndTest = useCallback(
    (pros: any) => {
      const examResult = createDetailedResultData();
      // Show confirmation with current progress
      const confirmMessage = `Đã trả lời: ${examResult.answeredCount}/${
        examResult.totalQuestions
      } câu\nCòn lại: ${
        examResult.totalQuestions - examResult.answeredCount
      } câu chưa trả lời`;

      // Move the async logic to the button press handler
      const handleSubmit = () => {
        try {
          console.log('📊 Exam Result:', examResult);
          const customer = store.getState().customer.data;
          const testResultData = {
            Id: randomGID(),
            LessonId: lessonData?.Id || 'unknown',
            CustomerId: customer?.Id,
            QuestionId: examResult.questionIds,
            AnswerId: examResult.answerIds,
            Score: examResult.totalScore,
            DateCreated: new Date().getTime(),
            Name: customer?.Name + ' ' + (lessonData?.Name || 'Lesson'),
            Status: examResult.isPassed ? StatusExam.passed : StatusExam.fail,
          };
          if(examResult.isPassed){
            dispatch(
              LessonActions.updateProcess({
                Id: lessonData?.Id,
                CourseId: lessonData?.CourseId,
                lessonId: lessonData?.Id,
                stepOrder: lessonData?.StepOrder,
                PartId: lessonData?.PartId,
                PercentCompleted: 100,
                Type: 'Lesson',
              }),
            );
          }
          // First hide the bottom sheet
          hideBottomSheet(pros.ref);

          // Then perform the async operations
          setTimeout(() => {
            testResultController
              .add([testResultData])
              .then(result => {
                if (result.code === 200) {
                  navigation.replace(RootScreen.resultTestNew, {
                    item: examResult,
                  });
                }
              })
              .catch(error => {
                console.error('❌ Error submitting exam:', error);
                console.log('Error submitting exam. Please try again.');
              });
          }, 0);
        } catch (error) {
          console.error('❌ Error submitting exam:', error);
          console.log('Error submitting exam. Please try again.');
        }
      };

      return (
        <View
          style={{
            height: 250,
            width: '100%',
            gap: 16,
            backgroundColor:
              ColorThemes.light.Neutral_Background_Color_Absolute,
            paddingHorizontal: 16,
          }}>
          <Text
            style={{
              ...TypoSkin.body3,
              color: ColorThemes.light.Neutral_Text_Color_Subtitle,
              textAlign: 'center',
              paddingTop: 8,
            }}>
            {confirmMessage}
          </Text>
          <AppButton
            title={t('exam.submitNow')}
            backgroundColor={ColorThemes.light.Primary_Color_Main}
            borderColor="transparent"
            containerStyle={{
              height: 45,
              marginTop: 16,
              borderRadius: 8,
            }}
            onPress={handleSubmit}
            textColor={ColorThemes.light.Neutral_Background_Color_Absolute}
          />
          <AppButton
            title={t('exam.continueExam')}
            backgroundColor={ColorThemes.light.Neutral_Background_Color_Main}
            borderColor="transparent"
            containerStyle={{
              height: 45,
              borderRadius: 8,
            }}
            onPress={() => {
              hideBottomSheet(pros.ref);
            }}
            textColor={ColorThemes.light.Neutral_Text_Color_Body}
          />
        </View>
      );
    },
    [createDetailedResultData, lessonData, testResultController, navigation, t],
  );

  // Sound reference
  const soundRef = useRef<Sound | null>(null);

  // Function to play audio
  const playAudio = (audioUrl: string) => {
    // Stop any currently playing sound
    stopAudio();

    // Set up sound
    Sound.setCategory('Playback');

    // Create new sound instance
    const sound = new Sound(audioUrl, '', error => {
      if (error) {
        console.log('Failed to load sound', error);
        setIsPlayingAudio(false);
        return;
      }

      // Play the sound
      sound.play(success => {
        if (success) {
          console.log('Sound played successfully');
          setIsPlayingAudio(undefined);
        } else {
          console.log('Sound playback failed');
        }
      });
    });

    // Save reference to sound
    soundRef.current = sound;
  };

  // Function to stop audio
  const stopAudio = () => {
    if (soundRef.current) {
      soundRef.current.stop();
      soundRef.current.release();
      soundRef.current = null;
      // setIsPlayingAudio(false);
    }
  };

  useEffect(() => {
    // Cleanup function to release sound resources when component unmounts
    return stopAudio();
  }, []);

  if (loading) {
    return (
      <SafeAreaView
        style={{
          flex: 1,
          backgroundColor:
            ColorThemes.light.Neutral_Background_Color_Absoluteary_Color_Main,
        }}>
        <View style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}>
          <ActivityIndicator color={ColorThemes.light.Primary_Color_Main} />
          <Text style={{marginTop: 16, ...TypoSkin.body2}}>
            {t('common.loading')}
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView
      style={{
        flex: 1,
        backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
      }}>
      <FBottomSheet ref={bottomSheetRef} />

      {/* Header with timer and submit button */}
      <ScreenHeader
        style={{paddingTop: Platform.OS === 'ios' ? 0 : 24}}
        action={
          <AppButton
            title={t('exam.submit')}
            containerStyle={{
              justifyContent: 'flex-start',
              alignSelf: 'baseline',
              paddingRight: 16,
            }}
            backgroundColor={'transparent'}
            textStyle={{
              ...TypoSkin.buttonText2,
              color: ColorThemes.light.Primary_Color_Main,
            }}
            borderColor="transparent"
            onPress={() => {
              showBottomSheet({
                ref: bottomSheetRef,
                enableDismiss: true,
                title: t('exam.confirmSubmit'),
                prefixAction: <View />,
                suffixAction: (
                  <TouchableOpacity
                    onPress={() => hideBottomSheet(bottomSheetRef)}
                    style={{padding: 6, alignItems: 'center'}}>
                    <Winicon
                      src="outline/layout/xmark"
                      size={20}
                      color={ColorThemes.light.Neutral_Text_Color_Body}
                    />
                  </TouchableOpacity>
                ),
                children: <ConFirmEndTest ref={bottomSheetRef} />,
              });
            }}
            textColor={ColorThemes.light.infor_main_color}
          />
        }
        title={
          <View
            style={{
              justifyContent: 'center',
              alignItems: 'center',
              paddingBottom: 8,
            }}>
            <Text
              style={{
                ...TypoSkin.title3,
                fontWeight: '700',
                color: ColorThemes.light.Neutral_Text_Color_Title,
              }}>
              {(() => {
                const answeredCount = Object.keys(
                  userAnswersRef.current,
                ).filter(questionId => {
                  const answers = userAnswersRef.current[questionId];
                  return (
                    answers &&
                    Object.values(answers).some(selected => selected === true)
                  );
                }).length;
                return `${answeredCount}/${exams
                  .map((exam: any) => exam.QuestionId?.split(',').length || 0)
                  .reduce((acc, curr) => acc + curr, 0)}`;
              })()}
            </Text>
          </View>
        }
        backIcon={
          <Winicon src="outline/arrows/left-arrow" size={20} />
        }
        onBack={() => {
          navigateBack();
          stopAudio();
        }}
      />

      {/* Exam Info - simplified without sections */}
      {selectedExam && (
        <View
          style={{
            backgroundColor: ColorThemes.light.neutral_main_background_color,
            borderBottomWidth: 1,
            borderBottomColor: ColorThemes.light.Neutral_Border_Color_Main,
          }}>
          <ScrollView
            style={{
              height: 'auto',
              maxHeight: Dimensions.get('window').height / 2.5,
            }}>
            {/* Exam Title */}
            <View
              style={{
                alignItems: 'center',
                paddingVertical: 8,
                paddingHorizontal: 16,
              }}>
              <Text
                style={{
                  ...TypoSkin.title3,
                  color: ColorThemes.light.neutral_text_title_color,
                  textAlign: 'center',
                  fontFamily: 'Noto Sans JP',
                }}>
                {selectedExam?.Name ||
                  `${t('exam.exam')} ${currentExamIndex + 1}`}
              </Text>
            </View>

            {/* Audio Player */}
            {selectedExam?.Audio && (
              <View
                style={{
                  paddingHorizontal: 16,
                  paddingVertical: 8,
                  backgroundColor:
                    ColorThemes.light.neutral_main_background_color,
                  borderRadius: 8,
                  marginHorizontal: 16,
                  marginBottom: 8,
                }}>
                <TouchableOpacity
                  disabled={
                    isPlayingAudio === true || isPlayingAudio === undefined
                  }
                  onPress={() => {
                    if (isPlayingAudio) {
                      // stopAudio();
                      return;
                    }
                    setIsPlayingAudio(true);
                    if (selectedExam?.Audio) {
                      const audioUrl = selectedExam.Audio?.includes('https')
                        ? selectedExam.Audio
                        : `${ConfigAPI.url.replace('/api/', '')}${
                            selectedExam.Audio
                          }`;
                      playAudio(audioUrl);
                    }
                  }}
                  style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                  }}>
                  <Winicon
                    src={
                      isPlayingAudio
                        ? 'color/multimedia/button-pause'
                        : 'outline/multimedia/sound'
                    }
                    size={16}
                    color={
                      isPlayingAudio === undefined
                        ? ColorThemes.light.Neutral_Text_Color_Subtitle
                        : ColorThemes.light.Info_Color_Main
                    }
                  />
                  <Text
                    style={{
                      ...TypoSkin.body3,
                      color:
                        isPlayingAudio === undefined
                          ? ColorThemes.light.Neutral_Text_Color_Subtitle
                          : ColorThemes.light.Info_Color_Main,
                      marginLeft: 8,
                      fontWeight: '600',
                    }}>
                    {isPlayingAudio === undefined
                      ? 'Đã nghe'
                      : isPlayingAudio
                      ? 'Đang nghe'
                      : 'Bấm để nghe'}
                  </Text>
                </TouchableOpacity>
              </View>
            )}

            {/* Collapsible Image */}
            {selectedExam?.Img && (
              <View
                style={{
                  marginHorizontal: 16,
                  marginBottom: 8,
                  borderRadius: 8,
                  overflow: 'hidden',
                  backgroundColor:
                    ColorThemes.light.neutral_main_background_color,
                }}>
                <View style={{padding: 12}}>
                  <ClickableImage
                    source={{uri: ConfigAPI.urlImg + selectedExam.Img}}
                    style={{
                      width: '100%',
                      height: 150,
                      borderRadius: 8,
                    }}
                    resizeMode="contain"
                  />
                </View>
              </View>
            )}
          </ScrollView>
        </View>
      )}

      {/* Questions Content */}
      <FlatList
        data={currentQuestions}
        keyExtractor={item => item.Id}
        renderItem={({item, index}) => (
          <QuestionItem
            question={item}
            questionIndex={index}
            onAnswerSelect={handleAnswerSelect}
            t={t}
          />
        )}
        contentContainerStyle={{paddingBottom: 50}}
        removeClippedSubviews={true}
        maxToRenderPerBatch={5}
        windowSize={10}
        initialNumToRender={3}
        getItemLayout={(_, index) => ({
          length: 200, // Estimated item height
          offset: 200 * index,
          index,
        })}
      />

      {/* Footer with Navigation and Question Counter */}
      <View
        style={{
          backgroundColor:
            ColorThemes.light.Neutral_Background_Color_Absoluteary_Color_Main,
          borderTopWidth: 1,
          borderTopColor: ColorThemes.light.Neutral_Border_Color_Main,
          paddingHorizontal: 16,
          paddingVertical: 12,
        }}>
        {/* Navigation Buttons - simplified without sections */}
        {exams.length > 1 && (
          <View
            style={{
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
            }}>
            {/* Back Button */}
            <AppButton
              backgroundColor={
                currentExamIndex > 0
                  ? ColorThemes.light.Primary_Color_Main
                  : ColorThemes.light.Neutral_Background_Color_Disable
              }
              textStyle={{
                ...TypoSkin.buttonText3,
                color:
                  ColorThemes.light.Neutral_Background_Color_Absolute,
              }}
              borderColor={ColorThemes.light.Neutral_Border_Color_Main}
              prefixIcon={'outline/arrows/left-arrow'}
              prefixIconSize={16}
              onPress={currentExamIndex > 0 ? handlePreviousExam : () => {}}
              textColor={
                ColorThemes.light.Neutral_Background_Color_Absolute
              }
              containerStyle={{
                paddingHorizontal: 12,
                marginRight: 8,
                borderRadius: 8,
              }}
            />

            {/* Exam Indicators */}
            <View
              style={{
                flex: 1,
                flexDirection: 'row',
                justifyContent: 'center',
              }}>
              <ScrollView
                horizontal
                showsHorizontalScrollIndicator={false}
                contentContainerStyle={{
                  gap: 4,
                  justifyContent: 'center',
                  flexGrow: 1,
                }}>
                {exams.map((exam, index) => (
                  <View
                    key={exam.Id}
                    style={{
                      width: 32,
                      height: 32,
                      backgroundColor:
                        currentExamIndex === index
                          ? ColorThemes.light.Primary_Color_Main
                          : ColorThemes.light.Neutral_Border_Color_Main,
                      borderRadius: 100,
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}>
                    <Text
                      style={{
                        ...TypoSkin.body2,
                        color:
                          currentExamIndex === index
                            ? ColorThemes.light
                                .Neutral_Background_Color_Absolute
                            : ColorThemes.light.Neutral_Text_Color_Body,
                        ...(currentExamIndex === index && {
                          fontWeight: '700',
                        }),
                      }}>
                      {index + 1}
                    </Text>
                  </View>
                ))}
              </ScrollView>
            </View>

            {/* Next Button */}
            <AppButton
              backgroundColor={
                currentExamIndex < exams.length - 1
                  ? ColorThemes.light.Primary_Color_Main
                  : ColorThemes.light.Neutral_Background_Color_Disable
              }
              textStyle={{
                ...TypoSkin.buttonText3,
                color:
                  ColorThemes.light.Neutral_Background_Color_Absolute,
              }}
              borderColor="transparent"
              suffixIcon={'outline/arrows/right-arrow'}
              suffixIconSize={16}
              onPress={currentExamIndex < exams.length - 1 ? handleNextExam : () => {}}
              textColor={
                ColorThemes.light.Neutral_Background_Color_Absolute
              }
              containerStyle={{
                paddingHorizontal: 12,
                marginLeft: 8,
                borderRadius: 8,
              }}
            />
          </View>
        )}
      </View>
    </SafeAreaView>
  );
}
